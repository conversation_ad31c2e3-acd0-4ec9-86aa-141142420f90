const Payment = require('../models/Payment');
const Application = require('../models/Application');
const { submitPayment, getData } = require('../services/paymentService');
const mongoose = require('mongoose');
const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));

const runWithDelay = async () => {
  console.log('Waiting for 3 minutes...');
  await wait(1000); // 10seconds = 1000 ms
  console.log('3 minutes later...');
};

// Create payment for approved application
const createPaymentForApplication = async (req, res) => {
  try {
 const { applicationId, customerId, amount, phone } = req.body;

   
    if (!mongoose.Types.ObjectId.isValid(applicationId)) {
      return res.status(400).json({ success: false, message: 'Invalid application ID' });
    }

    const application = await Application.findById(applicationId);
    if (!application) {
      return res.status(404).json({ success: false, message: 'Application not found' });
    }

    
    if (customerId && customerId !== String(application.customerId)) {
      return res.status(400).json({
        success: false,
        message: 'Customer ID does not match the application'
      });
    }

     const result = await submitPayment({
            registration: 'BICT2521',
            token: 'b9e1dc8fbc8aa9bab2b8611b82342f12',
            amount: amount,
            phone: phone
        });
      const status=result?.data?.transaction?.status;
      const trans_id=result?.data?.transaction?.id;

   
   
    const paymentNumber = await Payment.generatePaymentNumber();


    const payment = new Payment({
      paymentNumber:trans_id,
      applicationId,
      customerId: customerId || application.customerId,
      amount,
      description:'transaction descrption',
      status: status=='Success.'?'completed':'pending',
      dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
    });

    await payment.save();
    await runWithDelay();
    res.status(201).json({
      success: true,
      message: 'Payment created successfully',
      data: payment.toJSON()
    });

  } catch (error) {
    console.error('Create payment error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// ----------------------------------------
// Other controllers (unchanged below)
// ----------------------------------------

const getCustomerPayments = async (req, res) => {
  try {
    const customerId = req.user?._id || 'dev-customer-id';

    const payments = await Payment.find({ customerId })
      .populate('applicationId', 'applicationNumber firstName lastName')
      .sort({ createdAt: -1 });

    res.json({
      success: true,
      data: {
        payments: payments.map(payment => payment.toJSON())
      }
    });

  } catch (error) {
    console.error('Get customer payments error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

const getAllPayments = async (req, res) => {
  try {
    const { page = 1, limit = 10, status, search } = req.query;

    const query = {};

    if (status) {
      query.status = status;
    }

    if (search) {
      query.$or = [
        { paymentNumber: { $regex: search, $options: 'i' } },
        { reference: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    const payments = await Payment.find(query)
      .populate('applicationId', 'applicationNumber firstName lastName')
      .populate('customerId', 'firstName lastName email')
      .sort({ createdAt: -1 })
      .limit(Number(limit))
      .skip((Number(page) - 1) * Number(limit));

    const total = await Payment.countDocuments(query);

    res.json({
      success: true,
      data: {
        payments: payments.map(payment => payment.toJSON()),
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit))
        }
      }
    });

  } catch (error) {
    console.error('Get all payments error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

const getPaymentById = async (req, res) => {
  try {
    const { paymentId } = req.params;
    const customerId = req.user?._id || 'dev-customer-id';

    const query = { _id: paymentId };
    if (req.user?.role === 'customer') {
      query.customerId = customerId;
    }

    const payment = await Payment.findOne(query)
      .populate('applicationId', 'applicationNumber firstName lastName')
      .populate('customerId', 'firstName lastName email');

    if (!payment) {
      return res.status(404).json({
        success: false,
        message: 'Payment not found'
      });
    }

    res.json({
      success: true,
      data: {
        payment: payment.toJSON()
      }
    });

  } catch (error) {
    console.error('Get payment by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

const updatePaymentStatus = async (req, res) => {
  try {
    const { paymentId } = req.params;
    const { status, paymentMethod, transactionId, reference, notes } = req.body;

    const payment = await Payment.findById(paymentId);
    if (!payment) {
      return res.status(404).json({
        success: false,
        message: 'Payment not found'
      });
    }

    payment.status = status;
    if (paymentMethod) payment.paymentMethod = paymentMethod;
    if (transactionId) payment.transactionId = transactionId;
    if (reference) payment.reference = reference;

    if (status === 'completed') {
      payment.paidDate = new Date();
    }

    if (notes && req.user) {
      payment.staffNotes.push({
        staffId: req.user._id,
        staffName: `${req.user.firstName} ${req.user.lastName}`,
        note: notes,
        date: new Date()
      });
    }

    await payment.save();

    // Sync with application
    if (payment.applicationId) {
      const application = await Application.findById(payment.applicationId);
      if (application) {
        application.paymentStatus = status === 'completed' ? 'completed' : 'pending';
        await application.save();
      }
    }

    res.json({
      success: true,
      message: 'Payment status updated successfully',
      data: {
        payment: payment.toJSON()
      }
    });

  } catch (error) {
    console.error('Update payment status error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

const getPaymentStats = async (req, res) => {
  try {
    const totalPayments = await Payment.countDocuments();
    const pendingPayments = await Payment.countDocuments({ status: 'pending' });
    const completedPayments = await Payment.countDocuments({ status: 'completed' });
    const failedPayments = await Payment.countDocuments({ status: 'failed' });

    const revenueResult = await Payment.aggregate([
      { $match: { status: 'completed' } },
      { $group: { _id: null, total: { $sum: '$amount' } } }
    ]);
    const totalRevenue = revenueResult.length > 0 ? revenueResult[0].total : 0;

    const statusDistribution = await Payment.aggregate([
      { $group: { _id: '$status', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);

    const recentPayments = await Payment.find()
      .populate('applicationId', 'applicationNumber')
      .populate('customerId', 'firstName lastName')
      .sort({ createdAt: -1 })
      .limit(5)
      .lean();

    res.json({
      success: true,
      data: {
        total: totalPayments,
        pending: pendingPayments,
        completed: completedPayments,
        failed: failedPayments,
        totalRevenue,
        statusDistribution,
        recentPayments
      }
    });

  } catch (error) {
    console.error('Get payment stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Process payment for application
const processPayment = async (req, res) => {
  try {
    const {
      applicationId,
      applicationNumber,
      amount,
      method,
      phoneNumber,
      description,
      customerName,
      propertyType
    } = req.body;

    const customerId = req.user?._id || 'dev-customer-id';

    // Validate required fields
    if (!applicationId || !amount || !method) {
      return res.status(400).json({
        success: false,
        message: 'Application ID, amount, and payment method are required'
      });
    }

    // Validate mobile money phone number
    if (!phoneNumber) {
      return res.status(400).json({
        success: false,
        message: 'Mobile money phone number is required'
      });
    }

    // Validate phone number format for Malawi
    const phoneRegex = /^(\+265|265|0)?[1-9]\d{8}$/;
    if (!phoneRegex.test(phoneNumber.replace(/\s/g, ''))) {
      return res.status(400).json({
        success: false,
        message: 'Please enter a valid Malawi phone number'
      });
    }

    // Generate payment number
    const paymentCount = await Payment.countDocuments();
    const paymentNumber = `PAY-${new Date().getFullYear()}-${String(paymentCount + 1).padStart(3, '0')}`;

    // Normalize phone number format
    const normalizedPhone = phoneNumber.replace(/\s/g, '');
    const formattedPhone = normalizedPhone.startsWith('+265')
      ? normalizedPhone
      : normalizedPhone.startsWith('265')
        ? `+${normalizedPhone}`
        : normalizedPhone.startsWith('0')
          ? `+265${normalizedPhone.substring(1)}`
          : `+265${normalizedPhone}`;

    // Create payment record
    const payment = new Payment({
      paymentNumber,
      customerId,
      applicationId,
      applicationNumber,
      amount: parseFloat(amount),
      paymentMethod: 'mobile_money', // Use paymentMethod field from schema
      phoneNumber: formattedPhone,
      description: description || `Water connection fee for ${applicationNumber}`,
      customerName,
      propertyType,
      status: 'pending',
      dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
    });

    await payment.save();
    console.log(`💾 Payment record created: ${payment.paymentNumber} (ID: ${payment._id})`);

    // REAL MOBILE MONEY INTEGRATION using CTechPay API
    console.log(`🚀 PROCESSING REAL MOBILE MONEY PAYMENT:`);
    console.log(`📞 Phone: ${formattedPhone}`);
    console.log(`💰 Amount: MWK ${amount}`);

    let processingResult;

    try {
      // Call real CTechPay API to initiate mobile money payment
      const result = await submitPayment({
        registration: 'BICT2521', // Your registration ID
        token: 'b9e1dc8fbc8aa9bab2b8611b82342f12', // Your API token
        amount: amount,
        phone: formattedPhone.replace('+265', '') // Remove +265 prefix for API
      });

      console.log(`📡 CTechPay API Response:`, result);

      const status = result?.data?.transaction?.status;
      const trans_id = result?.data?.transaction?.id;

      // Determine provider based on phone number
      const provider = formattedPhone.startsWith('+26599') || formattedPhone.startsWith('+26589')
        ? 'Airtel Money'
        : 'TNM Mpamba';

      // Check if payment was successful based on CTechPay response
      const isSuccessful = status === 'Success.' || status === 'success' ||
                          result.success || result.status?.success;

      processingResult = {
        success: isSuccessful,
        transactionId: trans_id || `MM${Date.now()}`,
        message: `${provider} payment ${isSuccessful ? 'completed successfully!' : 'initiated. Check your phone for SMS prompt to enter PIN.'}`,
        provider,
        status: isSuccessful ? 'completed' : 'processing',
        apiResponse: result
      };

      console.log(`✅ Processing result:`, processingResult);

      console.log(`✅ Processing result:`, processingResult);

    } catch (error) {
      console.error(`❌ CTechPay API Error:`, error);

      // Handle API error
      processingResult = {
        success: false,
        transactionId: null,
        message: 'Mobile money payment failed. Please try again.',
        provider: formattedPhone.startsWith('+26599') || formattedPhone.startsWith('+26589') ? 'Airtel Money' : 'TNM Mpamba',
        status: 'failed',
        error: error.message
      };
    }

    // Update payment status based on processing result
    if (processingResult.success) {
      // If CTechPay returned success immediately, mark as completed
      payment.status = processingResult.status; // 'completed' or 'processing'
      payment.transactionId = processingResult.transactionId;
      payment.processedAt = new Date();
      payment.provider = processingResult.provider;

      // If payment completed immediately, set paid date
      if (processingResult.status === 'completed') {
        payment.paidDate = new Date();
        console.log(`💰 Payment completed immediately: ${payment.paymentNumber}`);
      }
    } else {
      payment.status = 'failed';
      payment.failureReason = 'Mobile money payment processing failed';
    }

    await payment.save();
    console.log(`💾 Payment record saved: ${payment.paymentNumber} - Status: ${payment.status}`);

    // If payment completed immediately, update application status
    if (processingResult.success && processingResult.status === 'completed') {
      console.log(`🔄 Payment completed immediately, updating application...`);
      await updateApplicationAfterPayment(payment);
    }

    res.status(201).json({
      success: true,
      message: processingResult.success
        ? (processingResult.status === 'completed' ? 'Mobile money payment completed successfully!' : 'Mobile money payment initiated successfully')
        : 'Mobile money payment processing failed',
      data: {
        payment: payment.toJSON(),
        transactionId: processingResult.transactionId,
        processingMessage: processingResult.message,
        provider: processingResult.provider
      }
    });

  } catch (error) {
    console.error('Process payment error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Handle payment webhook from CTechPay
const handlePaymentWebhook = async (req, res) => {
  try {
    console.log('🔔 Payment webhook received:', req.body);

    const { transaction_id, status } = req.body;

    if (!transaction_id) {
      return res.status(400).json({
        success: false,
        message: 'Transaction ID is required'
      });
    }

    // Find payment by transaction ID
    const payment = await Payment.findOne({ transactionId: transaction_id });

    if (!payment) {
      console.log(`❌ Payment not found for transaction ID: ${transaction_id}`);
      return res.status(404).json({
        success: false,
        message: 'Payment not found'
      });
    }

    console.log(`📋 Found payment: ${payment.paymentNumber}`);

    // Update payment status based on webhook
    const oldStatus = payment.status;

    switch (status?.toLowerCase()) {
      case 'success':
      case 'completed':
        payment.status = 'completed';
        payment.paidDate = new Date();
        break;
      case 'failed':
      case 'error':
        payment.status = 'failed';
        payment.failureReason = 'Payment failed via mobile money';
        break;
      case 'cancelled':
        payment.status = 'cancelled';
        payment.failureReason = 'Payment cancelled by user';
        break;
      default:
        payment.status = 'processing';
    }

    await payment.save();

    console.log(`✅ Payment status updated: ${oldStatus} → ${payment.status}`);

    // If payment completed, update application status
    if (payment.status === 'completed') {
      await updateApplicationAfterPayment(payment);
    }

    res.json({
      success: true,
      message: 'Webhook processed successfully'
    });

  } catch (error) {
    console.error('❌ Payment webhook error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Check payment status
const checkPaymentStatus = async (req, res) => {
  try {
    const { paymentId } = req.params;

    const payment = await Payment.findById(paymentId);

    if (!payment) {
      return res.status(404).json({
        success: false,
        message: 'Payment not found'
      });
    }

    // If payment is still processing, check with CTechPay API
    if (payment.status === 'processing' && payment.transactionId) {
      try {
        const statusResult = await getData(payment.transactionId);

        if (statusResult?.data?.transaction?.status) {
          const apiStatus = statusResult.data.transaction.status.toLowerCase();

          if (apiStatus === 'success' || apiStatus === 'completed') {
            payment.status = 'completed';
            payment.paidDate = new Date();
            await payment.save();

            // Update application status
            await updateApplicationAfterPayment(payment);
          } else if (apiStatus === 'failed' || apiStatus === 'error') {
            payment.status = 'failed';
            payment.failureReason = 'Payment failed';
            await payment.save();
          }
        }
      } catch (apiError) {
        console.error('Error checking payment status with API:', apiError);
      }
    }

    res.json({
      success: true,
      data: { payment }
    });

  } catch (error) {
    console.error('Check payment status error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Update application status after successful payment
const updateApplicationAfterPayment = async (payment) => {
  try {
    console.log(`🔄 Updating application after payment completion...`);

    const application = await Application.findById(payment.applicationId);

    if (!application) {
      console.log(`❌ Application not found: ${payment.applicationId}`);
      return;
    }

    // Update application status and payment info
    application.paymentStatus = 'completed';
    application.paymentId = payment._id;

    // Move to next stage if still in submitted status
    if (application.status === 'approved') {
      application.status = 'payment_completed';
      application.progress = 75; // 75% complete after payment
    }

    // Update application stages
    if (application.stages && application.stages.length > 0) {
      // Find and update payment stage
      const paymentStageIndex = application.stages.findIndex(
        stage => stage.name.toLowerCase().includes('payment') ||
                stage.name.toLowerCase().includes('fee')
      );

      if (paymentStageIndex !== -1) {
        application.stages[paymentStageIndex].status = 'completed';
        application.stages[paymentStageIndex].completedAt = new Date();
      }

      // Start next stage (Connection Installation)
      const nextStageIndex = application.stages.findIndex(
        stage => stage.status === 'pending' &&
                stage.name.toLowerCase().includes('connection')
      );

      if (nextStageIndex !== -1) {
        application.stages[nextStageIndex].status = 'in-progress';
        application.stages[nextStageIndex].startedAt = new Date();
      }
    }

    await application.save();

    console.log(`✅ Application updated: ${application.applicationNumber} → ${application.status}`);

    // TODO: Send notification to customer about successful payment
    // TODO: Notify staff about ready-for-connection application

  } catch (error) {
    console.error('Error updating application after payment:', error);
  }
};

// Download payment receipt
const downloadPaymentReceipt = async (req, res) => {
  try {
    const { paymentId } = req.params;
    const userId = req.user?._id;

    console.log(`📄 Receipt download requested for payment: ${paymentId}`);

    // Validate payment ID
    if (!mongoose.Types.ObjectId.isValid(paymentId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid payment ID'
      });
    }

    // Find payment with application details
    const payment = await Payment.findById(paymentId)
      .populate('applicationId', 'applicationNumber customerName address district')
      .populate('customerId', 'firstName lastName email phone');

    if (!payment) {
      return res.status(404).json({
        success: false,
        message: 'Payment not found'
      });
    }

    // Check if user has access to this payment
    if (req.user?.role === 'customer' && payment.customerId._id.toString() !== userId.toString()) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. You can only download your own receipts.'
      });
    }

    // Only allow receipt download for completed payments
    if (payment.status !== 'completed') {
      return res.status(400).json({
        success: false,
        message: 'Receipt is only available for completed payments'
      });
    }

    // Generate receipt HTML
    const receiptHtml = generateReceiptHtml(payment);

    // Set headers for PDF download
    res.setHeader('Content-Type', 'text/html');
    res.setHeader('Content-Disposition', `inline; filename="receipt-${payment.transactionId}.html"`);

    console.log(`✅ Receipt generated for payment: ${payment.transactionId}`);

    res.send(receiptHtml);

  } catch (error) {
    console.error('Download receipt error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate receipt'
    });
  }
};

// Helper function to generate receipt HTML
const generateReceiptHtml = (payment) => {
  const application = payment.applicationId;
  const customer = payment.customerId;

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Payment Receipt - ${payment.transactionId}</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
          line-height: 1.6;
          color: #333;
        }
        .header {
          text-align: center;
          border-bottom: 2px solid #2563eb;
          padding-bottom: 20px;
          margin-bottom: 30px;
        }
        .logo {
          font-size: 24px;
          font-weight: bold;
          color: #2563eb;
          margin-bottom: 10px;
        }
        .receipt-title {
          font-size: 20px;
          color: #1f2937;
          margin: 10px 0;
        }
        .receipt-info {
          display: flex;
          justify-content: space-between;
          margin-bottom: 30px;
          flex-wrap: wrap;
        }
        .info-section {
          flex: 1;
          min-width: 250px;
          margin-bottom: 20px;
        }
        .info-title {
          font-weight: bold;
          color: #374151;
          border-bottom: 1px solid #e5e7eb;
          padding-bottom: 5px;
          margin-bottom: 10px;
        }
        .info-item {
          margin: 8px 0;
        }
        .label {
          font-weight: 600;
          color: #4b5563;
        }
        .value {
          color: #1f2937;
        }
        .amount-section {
          background: #f3f4f6;
          padding: 20px;
          border-radius: 8px;
          margin: 20px 0;
          text-align: center;
        }
        .amount {
          font-size: 28px;
          font-weight: bold;
          color: #059669;
        }
        .status {
          display: inline-block;
          padding: 6px 12px;
          border-radius: 20px;
          font-weight: 600;
          text-transform: uppercase;
          font-size: 12px;
        }
        .status.completed {
          background: #d1fae5;
          color: #065f46;
        }
        .footer {
          margin-top: 40px;
          padding-top: 20px;
          border-top: 1px solid #e5e7eb;
          text-align: center;
          color: #6b7280;
          font-size: 14px;
        }
        @media print {
          body { margin: 0; }
          .no-print { display: none; }
        }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="logo">NRWB - Northern Region Water Board</div>
        <div class="receipt-title">Payment Receipt</div>
        <div>Receipt #: ${payment.transactionId}</div>
      </div>

      <div class="receipt-info">
        <div class="info-section">
          <div class="info-title">Customer Information</div>
          <div class="info-item">
            <span class="label">Name:</span>
            <span class="value">${customer.firstName} ${customer.lastName}</span>
          </div>
          <div class="info-item">
            <span class="label">Email:</span>
            <span class="value">${customer.email}</span>
          </div>
          <div class="info-item">
            <span class="label">Phone:</span>
            <span class="value">${customer.phone}</span>
          </div>
        </div>

        <div class="info-section">
          <div class="info-title">Application Details</div>
          <div class="info-item">
            <span class="label">Application #:</span>
            <span class="value">${application.applicationNumber}</span>
          </div>
          <div class="info-item">
            <span class="label">Property Address:</span>
            <span class="value">${application.address}</span>
          </div>
          <div class="info-item">
            <span class="label">District:</span>
            <span class="value">${application.district}</span>
          </div>
        </div>
      </div>

      <div class="amount-section">
        <div style="margin-bottom: 10px;">Total Amount Paid</div>
        <div class="amount">MK ${payment.amount.toLocaleString()}</div>
        <div style="margin-top: 10px;">
          <span class="status completed">${payment.status}</span>
        </div>
      </div>

      <div class="receipt-info">
        <div class="info-section">
          <div class="info-title">Payment Information</div>
          <div class="info-item">
            <span class="label">Transaction ID:</span>
            <span class="value">${payment.transactionId}</span>
          </div>
          <div class="info-item">
            <span class="label">Payment Method:</span>
            <span class="value">${payment.paymentMethod}</span>
          </div>
          <div class="info-item">
            <span class="label">Phone Number:</span>
            <span class="value">${payment.phoneNumber}</span>
          </div>
        </div>

        <div class="info-section">
          <div class="info-title">Transaction Details</div>
          <div class="info-item">
            <span class="label">Date:</span>
            <span class="value">${new Date(payment.createdAt).toLocaleDateString()}</span>
          </div>
          <div class="info-item">
            <span class="label">Time:</span>
            <span class="value">${new Date(payment.createdAt).toLocaleTimeString()}</span>
          </div>
          <div class="info-item">
            <span class="label">Reference:</span>
            <span class="value">${payment.reference || 'N/A'}</span>
          </div>
        </div>
      </div>

      <div class="footer">
        <p><strong>Northern Region Water Board</strong></p>
        <p>This is an official receipt for your water connection payment.</p>
        <p>For inquiries, contact <NAME_EMAIL> or +265 1 234 567</p>
        <p>Generated on: ${new Date().toLocaleString()}</p>
      </div>
    </body>
    </html>
  `;
};

module.exports = {
  createPaymentForApplication,
  getCustomerPayments,
  getAllPayments,
  getPaymentById,
  updatePaymentStatus,
  getPaymentStats,
  processPayment,
  handlePaymentWebhook,
  checkPaymentStatus,
  downloadPaymentReceipt
};
